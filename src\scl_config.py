"""
SCL Configuration for Your Project
你的项目的SCL配置
"""

# SCL模块配置
SCL_CONFIG = {
    # 基本参数
    'temperature': 0.5,          # 对比学习温度参数
    'feature_dim': 768,          # 特征维度 (根据你的模型调整)
    'patch_size': 14,            # 特征图块大小
    'use_sam_guidance': True,    # 是否使用SAM指导
    
    # 训练参数
    'lr': 0.0005,               # 学习率
    'weight_decay': 1e-4,       # 权重衰减
    'epochs': 10,               # SCL训练轮数
    'clip_grad': 1.0,           # 梯度裁剪
    
    # 数据集特定参数
    'mvtec_sam_suffix': '-sam-b',     # MVTec数据集SAM文件后缀
    'btad_sam_suffix': '-sam-b',      # BTAD数据集SAM文件后缀
    'itd_sam_suffix': '-sam-b',       # ITD数据集SAM文件后缀
}

# 数据集路径配置
DATASET_PATHS = {
    'mvtec': '../mvtec_anomaly_detection',
    'btad': '../BTAD', 
    'itd': '../ITD'
}

# SCL损失权重 (可以根据实验调整)
SCL_LOSS_WEIGHTS = {
    'mvtec': 0.1,    # MVTec数据集的SCL损失权重
    'btad': 0.1,     # BTAD数据集的SCL损失权重  
    'itd': 0.1,      # ITD数据集的SCL损失权重
}
