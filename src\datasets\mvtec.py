import os
from enum import Enum
import PIL
import torch
from torchvision import transforms
import numpy as np

# Custom ToTensor to fix numpy compatibility issues
class CompatibleToTensor:
    """Custom ToTensor that handles numpy compatibility issues."""

    def __call__(self, pic):
        """Convert PIL Image to tensor with numpy compatibility fix."""
        try:
            # First try the standard torchvision method
            return transforms.functional.to_tensor(pic)
        except (TypeError, RuntimeError):
            # Fallback: manual conversion
            if hasattr(pic, 'mode'):
                # PIL Image
                pic_array = np.array(pic, dtype=np.uint8)
            else:
                # Already an array-like object
                pic_array = np.array(pic, dtype=np.uint8)

            # Ensure 3D array
            if pic_array.ndim == 2:
                pic_array = np.expand_dims(pic_array, axis=2)
            elif pic_array.ndim == 3 and pic_array.shape[2] == 1:
                pass  # Grayscale is fine
            elif pic_array.ndim == 3 and pic_array.shape[2] == 3:
                pass  # RGB is fine
            elif pic_array.ndim == 3 and pic_array.shape[2] == 4:
                pic_array = pic_array[:, :, :3]  # Remove alpha channel

            # Convert to tensor manually to avoid numpy type issues
            # Transpose from HWC to CHW
            pic_array = pic_array.transpose((2, 0, 1))

            # Create tensor directly from array data
            tensor = torch.tensor(pic_array, dtype=torch.uint8)

            # Convert to float and normalize
            return tensor.float().div(255.0)

_CLASSNAMES = [
    "bottle",
    "cable",
    "capsule",
    "carpet",
    "grid",
    "hazelnut",
    "leather",
    "metal_nut",
    "pill",
    "screw",
    "tile",
    "toothbrush",
    "transistor",
    "wood",
    "zipper",
]

IMAGENET_MEAN = [0.485, 0.456, 0.406]
IMAGENET_STD = [0.229, 0.224, 0.225]


class DatasetSplit(Enum):
    TRAIN = "train"
    VAL = "val"
    TEST = "test"


class MVTecDataset(torch.utils.data.Dataset):
    """
    PyTorch Dataset for MVTec.
    """

    def __init__(
        self,
        source,
        classname,
        resize=256,
        imagesize=224,
        split=DatasetSplit.TRAIN,
        train_val_split=1.0,
        **kwargs,
    ):
        """
        Args:
            source: [str]. Path to the MVTec data folder.
            classname: [str or None]. Name of MVTec class that should be
                       provided in this dataset. If None, the datasets
                       iterates over all available images.
            resize: [int]. (Square) Size the loaded image initially gets
                    resized to.
            imagesize: [int]. (Square) Size the resized loaded image gets
                       (center-)cropped to.
            split: [enum-option]. Indicates if training or test split of the
                   data should be used. Has to be an option taken from
                   DatasetSplit, e.g. mvtec.DatasetSplit.TRAIN. Note that
                   mvtec.DatasetSplit.TEST will also load mask data.
        """
        super().__init__()
        self.normal_class = "good"
        self.set_normal_class()
        self.source = source
        self.split = split
        self.classnames_to_use = [classname] if classname is not None else _CLASSNAMES
        self.train_val_split = train_val_split

        self.imgpaths_per_class, self.data_to_iterate = self.get_image_data()

        self.transform_img = [
            transforms.Resize(resize),
            transforms.CenterCrop(imagesize),
            CompatibleToTensor(),  # Use our compatible version
            transforms.Normalize(mean=IMAGENET_MEAN, std=IMAGENET_STD),
        ]
        self.transform_img = transforms.Compose(self.transform_img)

        self.transform_mask = [
            transforms.Resize(resize),
            transforms.CenterCrop(imagesize),
            CompatibleToTensor(),  # Use our compatible version
        ]
        self.transform_mask = transforms.Compose(self.transform_mask)

        self.imagesize = (3, imagesize, imagesize)
        self.transform_std = IMAGENET_STD
        self.transform_mean = IMAGENET_MEAN

    def set_normal_class(self):
        self.normal_class = "good"

    def __getitem__(self, idx):
        classname, anomaly, image_path, mask_path = self.data_to_iterate[idx]
        image = PIL.Image.open(image_path).convert("RGB")
        image = self.transform_img(image)

        if self.split == DatasetSplit.TEST and mask_path is not None:
            mask = PIL.Image.open(mask_path)
            mask = self.transform_mask(mask)
        else:
            mask = torch.zeros([1, *image.size()[1:]])

        return {
            "image": image,
            "mask": mask,
            "classname": classname,
            "anomaly": anomaly,
            "is_anomaly": int(anomaly != self.normal_class),
            "image_name": "/".join(image_path.split("/")[-4:]),
            "image_path": image_path,
            # "mask_path": mask_path,
        }

    def __len__(self):
        return len(self.data_to_iterate)

    def get_image_data(self):
        imgpaths_per_class = {}
        maskpaths_per_class = {}

        for classname in self.classnames_to_use:
            classpath = os.path.join(self.source, classname, self.split.value)
             # 过滤掉 .DS_Store 和 ._ 开头的隐藏文件
            anomaly_types = [f for f in os.listdir(classpath) if not f.startswith("._") ]
            maskpath = os.path.join(self.source, classname, "ground_truth")
            anomaly_types = os.listdir(classpath)

            imgpaths_per_class[classname] = {}
            maskpaths_per_class[classname] = {}

            for anomaly in anomaly_types:
                anomaly_path = os.path.join(classpath, anomaly)
                if not os.path.isdir(anomaly_path):  # 再次检查是否为有效目录
                    continue
                anomaly_files = sorted(os.listdir(anomaly_path))
                imgpaths_per_class[classname][anomaly] = [
                    os.path.join(anomaly_path, x) for x in anomaly_files
                ]

                if self.train_val_split < 1.0:
                    n_images = len(imgpaths_per_class[classname][anomaly])
                    train_val_split_idx = int(n_images * self.train_val_split)
                    if self.split == DatasetSplit.TRAIN:
                        imgpaths_per_class[classname][anomaly] = imgpaths_per_class[
                            classname
                        ][anomaly][:train_val_split_idx]
                    elif self.split == DatasetSplit.VAL:
                        imgpaths_per_class[classname][anomaly] = imgpaths_per_class[
                            classname
                        ][anomaly][train_val_split_idx:]

                if self.split == DatasetSplit.TEST and anomaly != self.normal_class:
                    anomaly_mask_path = os.path.join(maskpath, anomaly)
                    anomaly_mask_files = sorted(os.listdir(anomaly_mask_path))
                    maskpaths_per_class[classname][anomaly] = [
                        os.path.join(anomaly_mask_path, x) for x in anomaly_mask_files
                    ]
                else:
                    maskpaths_per_class[classname][self.normal_class] = None

        # Unrolls the data dictionary to an easy-to-iterate list.
        data_to_iterate = []
        for classname in sorted(imgpaths_per_class.keys()):
            for anomaly in sorted(imgpaths_per_class[classname].keys()):
                for i, image_path in enumerate(imgpaths_per_class[classname][anomaly]):
                    data_tuple = [classname, anomaly, image_path]
                    if self.split == DatasetSplit.TEST and anomaly != self.normal_class:
                        data_tuple.append(maskpaths_per_class[classname][anomaly][i])
                    else:
                        data_tuple.append(None)
                    data_to_iterate.append(data_tuple)
        data_to_iterate = np.array(data_to_iterate)
        return imgpaths_per_class, data_to_iterate
