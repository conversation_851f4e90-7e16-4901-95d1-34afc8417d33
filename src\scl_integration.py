"""
SCL Integration Module
SCL集成模块

这个模块提供了将SCL集成到现有模型中的功能。
"""

import torch
import torch.nn as nn
from typing import Dict, List, Optional, Union, Any
from src.scl_module import SCLModule, SAMGuidanceLoader, create_scl_module
from src.scl_config import SCL_CONFIG


class SCLIntegratedModel(nn.Module):
    """
    SCL集成模型
    将SCL功能集成到现有模型中
    """

    def __init__(self, base_model: nn.Module, scl_config: Dict):
        """
        初始化SCL集成模型

        Args:
            base_model: 基础模型
            scl_config: SCL配置
        """
        super(SCLIntegratedModel, self).__init__()

        self.base_model = base_model
        self.scl_config = scl_config
        self.training_mode = 'normal'  # 'normal' or 'scl'

        # 创建SCL模块
        self.scl_module = create_scl_module(scl_config)

        # SAM指导加载器
        if scl_config.get('use_sam_guidance', True):
            sam_suffix = scl_config.get('mvtec_sam_suffix', '-sam-b')
            self.sam_loader = SAMGuidanceLoader(sam_suffix)
        else:
            self.sam_loader = None

        # 特征提取钩子
        self.feature_hooks = []
        self.extracted_features = []

    def set_training_mode(self, mode: str):
        """
        设置训练模式

        Args:
            mode: 'normal' 或 'scl'
        """
        self.training_mode = mode
        if mode == 'scl':
            self.scl_module.train()
        else:
            self.scl_module.eval()

    def forward(self, x: torch.Tensor, image_paths: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        前向传播

        Args:
            x: 输入张量
            image_paths: 图像路径列表（用于SCL训练）

        Returns:
            包含模型输出和SCL损失的字典
        """
        # 基础模型前向传播
        if hasattr(self.base_model, 'forward'):
            base_output = self.base_model(x)
        else:
            base_output = self.base_model(x)

        result = {'base_output': base_output}

        # 如果是SCL训练模式，计算SCL损失
        if self.training_mode == 'scl' and self.training:
            scl_output = self._compute_scl_loss(x, image_paths)
            result.update(scl_output)

        return result

    def _compute_scl_loss(self, x: torch.Tensor, image_paths: Optional[List[str]] = None) -> Dict[str, torch.Tensor]:
        """
        计算SCL损失

        Args:
            x: 输入张量
            image_paths: 图像路径列表

        Returns:
            SCL损失字典
        """
        # 提取特征
        features = self._extract_features_for_scl(x)

        # 加载SAM掩码
        sam_masks = None
        if self.sam_loader is not None and image_paths is not None:
            sam_masks = self.sam_loader.load_batch_sam_masks(image_paths)
            if sam_masks is not None:
                sam_masks = sam_masks.to(x.device)

        # 计算SCL损失
        scl_output = self.scl_module(features, sam_masks, image_paths)

        return scl_output

    def _extract_features_for_scl(self, x: torch.Tensor) -> torch.Tensor:
        """
        为SCL提取特征

        Args:
            x: 输入张量

        Returns:
            特征张量 [B, N, D]
        """
        # 这里需要根据具体的基础模型来实现特征提取
        # 以下是一个通用的实现示例

        if hasattr(self.base_model, '_embed'):
            # 如果基础模型有_embed方法（如SoftPatch）
            features = self.base_model._embed(x, detach=False)
            if isinstance(features, (list, tuple)):
                features = features[0]  # 取第一个特征
        elif hasattr(self.base_model, 'extract_features'):
            # 如果基础模型有extract_features方法
            features = self.base_model.extract_features(x)
        else:
            # 默认实现：使用基础模型的前向传播
            with torch.no_grad():
                features = self.base_model(x)
                if isinstance(features, dict):
                    features = features.get('features', features.get('output', list(features.values())[0]))

        # 确保特征是正确的形状 [B, N, D]
        if features.dim() == 2:
            # [B*N, D] -> [B, N, D]
            batch_size = x.shape[0]
            num_patches = features.shape[0] // batch_size
            features = features.view(batch_size, num_patches, -1)
        elif features.dim() == 4:
            # [B, D, H, W] -> [B, H*W, D]
            B, D, H, W = features.shape
            features = features.permute(0, 2, 3, 1).contiguous().view(B, H*W, D)

        return features

    def extract_features(self, x: torch.Tensor) -> torch.Tensor:
        """
        提取特征（用于外部调用）

        Args:
            x: 输入张量

        Returns:
            特征张量
        """
        return self._extract_features_for_scl(x)

    def parameters(self):
        """
        返回所有可训练参数
        """
        # 在SCL训练模式下，只训练SCL模块的参数
        if self.training_mode == 'scl':
            return self.scl_module.parameters()
        else:
            # 正常模式下训练所有参数
            return super().parameters()


class SCLPatchCoreIntegration:
    """
    SCL与PatchCore的集成类
    专门用于将SCL集成到PatchCore类型的模型中
    """

    def __init__(self, patchcore_model, scl_config: Dict):
        """
        初始化SCL-PatchCore集成

        Args:
            patchcore_model: PatchCore模型实例
            scl_config: SCL配置
        """
        self.patchcore_model = patchcore_model
        self.scl_config = scl_config
        self.scl_integrated_model = SCLIntegratedModel(patchcore_model, scl_config)

    def fit_with_scl(self, training_data, scl_epochs: int = 10, scl_lr: float = 0.0005):
        """
        使用SCL进行训练

        Args:
            training_data: 训练数据
            scl_epochs: SCL训练轮数
            scl_lr: SCL学习率
        """
        print("开始SCL增强训练...")

        # 设置SCL训练模式
        self.scl_integrated_model.set_training_mode('scl')
        self.scl_integrated_model.train()

        # 创建优化器（只优化SCL模块）
        optimizer = torch.optim.Adam(
            self.scl_integrated_model.scl_module.parameters(),
            lr=scl_lr,
            weight_decay=self.scl_config.get('weight_decay', 1e-4)
        )

        for epoch in range(scl_epochs):
            total_loss = 0.0
            num_batches = 0

            for batch_idx, batch in enumerate(training_data):
                # 处理批次数据
                if isinstance(batch, dict):
                    images = batch['image']
                    image_paths = batch.get('image_path', None)
                else:
                    images = batch
                    image_paths = None

                if torch.cuda.is_available():
                    images = images.cuda()

                # SCL前向传播
                output = self.scl_integrated_model(images, image_paths)
                scl_loss = output.get('scl_loss', torch.tensor(0.0))

                # 反向传播
                optimizer.zero_grad()
                if scl_loss.item() > 0:
                    scl_loss.backward()
                    torch.nn.utils.clip_grad_norm_(
                        self.scl_integrated_model.scl_module.parameters(),
                        self.scl_config.get('clip_grad', 1.0)
                    )
                    optimizer.step()

                total_loss += scl_loss.item()
                num_batches += 1

                if batch_idx % 10 == 0:
                    print(f"Epoch {epoch}, Batch {batch_idx}: SCL Loss = {scl_loss.item():.6f}")

            avg_loss = total_loss / max(num_batches, 1)
            print(f"Epoch {epoch} 完成: 平均SCL损失 = {avg_loss:.6f}")

        print("SCL训练完成!")

        # 切换回正常模式
        self.scl_integrated_model.set_training_mode('normal')

        # 继续原始的PatchCore训练
        print("开始原始PatchCore训练...")
        self.patchcore_model.fit(training_data)

    def predict(self, test_data):
        """
        预测

        Args:
            test_data: 测试数据

        Returns:
            预测结果
        """
        # 使用原始PatchCore模型进行预测
        return self.patchcore_model.predict(test_data)


def integrate_scl_to_model(model, scl_config: Optional[Dict] = None) -> SCLIntegratedModel:
    """
    将SCL集成到任意模型的便捷函数

    Args:
        model: 要集成SCL的模型
        scl_config: SCL配置，如果为None则使用默认配置

    Returns:
        SCL集成模型
    """
    if scl_config is None:
        scl_config = SCL_CONFIG.copy()

    return SCLIntegratedModel(model, scl_config)