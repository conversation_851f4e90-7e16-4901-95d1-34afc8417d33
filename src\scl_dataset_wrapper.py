"""
SCL Dataset Wrapper
SCL数据集包装器

这个模块提供了将现有数据集包装为SCL兼容格式的功能。
"""

import torch
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Optional, Union, Any
import os
from pathlib import Path
import numpy as np


class SCLDatasetWrapper(Dataset):
    """
    SCL数据集包装器
    将现有数据集包装为SCL兼容的格式
    """

    def __init__(self, original_dataset: Dataset, add_image_paths: bool = True):
        """
        初始化SCL数据集包装器

        Args:
            original_dataset: 原始数据集
            add_image_paths: 是否添加图像路径信息
        """
        self.dataset = original_dataset
        self.add_image_paths = add_image_paths

    def __len__(self):
        return len(self.dataset)

    def __getitem__(self, idx):
        # 获取原始数据
        item = self.dataset[idx]

        # 确保返回字典格式
        if isinstance(item, dict):
            result = item.copy()
        else:
            # 如果原始数据不是字典，转换为字典
            if isinstance(item, (list, tuple)) and len(item) >= 2:
                result = {'image': item[0], 'label': item[1]}
                if len(item) > 2:
                    result['mask'] = item[2]
            else:
                result = {'image': item, 'label': 0}

        # 添加图片路径信息
        if self.add_image_paths and 'image_path' not in result:
            image_path = self._get_image_path(idx)
            if image_path:
                result['image_path'] = image_path

        # 确保必要的字段存在
        if 'is_anomaly' not in result:
            # 根据label或classname推断是否为异常
            if 'label' in result:
                result['is_anomaly'] = result['label'] != 0
            elif 'classname' in result:
                result['is_anomaly'] = result['classname'] != 'good'
            else:
                result['is_anomaly'] = False

        return result

    def _get_image_path(self, idx) -> Optional[str]:
        """
        获取图像路径

        Args:
            idx: 数据索引

        Returns:
            图像路径或None
        """
        try:
            # 尝试从原始数据集获取路径
            if hasattr(self.dataset, 'data_to_iterate'):
                # MVTec风格的数据集
                if idx < len(self.dataset.data_to_iterate):
                    return self.dataset.data_to_iterate[idx][2]  # image_path
            elif hasattr(self.dataset, 'image_paths'):
                # 如果数据集有image_paths属性
                if idx < len(self.dataset.image_paths):
                    return self.dataset.image_paths[idx]
            elif hasattr(self.dataset, 'samples'):
                # ImageFolder风格的数据集
                if idx < len(self.dataset.samples):
                    return self.dataset.samples[idx][0]
            elif hasattr(self.dataset, 'imgs'):
                # 另一种ImageFolder风格
                if idx < len(self.dataset.imgs):
                    return self.dataset.imgs[idx][0]

            # 如果无法获取路径，生成默认路径
            return f"dataset/image_{idx:06d}.png"

        except Exception as e:
            print(f"获取图像路径失败 (idx={idx}): {e}")
            return f"dataset/image_{idx:06d}.png"


class SCLMVTecWrapper(SCLDatasetWrapper):
    """
    专门用于MVTec数据集的SCL包装器
    """

    def __init__(self, mvtec_dataset, sam_suffix='-sam-b'):
        """
        初始化MVTec SCL包装器

        Args:
            mvtec_dataset: MVTec数据集实例
            sam_suffix: SAM文件后缀
        """
        super().__init__(mvtec_dataset, add_image_paths=True)
        self.sam_suffix = sam_suffix

    def __getitem__(self, idx):
        result = super().__getitem__(idx)

        # 添加SAM掩码路径
        if 'image_path' in result:
            sam_path = self._get_sam_path(result['image_path'])
            if sam_path and os.path.exists(sam_path):
                result['sam_path'] = sam_path

        return result

    def _get_sam_path(self, image_path: str) -> Optional[str]:
        """
        获取对应的SAM掩码路径

        Args:
            image_path: 图像路径

        Returns:
            SAM掩码路径或None
        """
        try:
            image_path = Path(image_path)
            sam_path = image_path.parent / f"{image_path.stem}{self.sam_suffix}.npy"
            return str(sam_path)
        except Exception:
            return None


class SCLBTADWrapper(SCLDatasetWrapper):
    """
    专门用于BTAD数据集的SCL包装器
    """

    def __init__(self, btad_dataset, sam_suffix='-sam-b'):
        """
        初始化BTAD SCL包装器

        Args:
            btad_dataset: BTAD数据集实例
            sam_suffix: SAM文件后缀
        """
        super().__init__(btad_dataset, add_image_paths=True)
        self.sam_suffix = sam_suffix


class SCLVISAWrapper(SCLDatasetWrapper):
    """
    专门用于VISA数据集的SCL包装器
    """

    def __init__(self, visa_dataset, sam_suffix='-sam-b'):
        """
        初始化VISA SCL包装器

        Args:
            visa_dataset: VISA数据集实例
            sam_suffix: SAM文件后缀
        """
        super().__init__(visa_dataset, add_image_paths=True)
        self.sam_suffix = sam_suffix


def wrap_dataset_for_scl(dataset: Dataset, dataset_type: str = 'auto', sam_suffix: str = '-sam-b') -> SCLDatasetWrapper:
    """
    将数据集包装为SCL兼容格式的便捷函数

    Args:
        dataset: 原始数据集
        dataset_type: 数据集类型 ('mvtec', 'btad', 'visa', 'auto')
        sam_suffix: SAM文件后缀

    Returns:
        SCL包装的数据集
    """
    if dataset_type == 'auto':
        # 自动检测数据集类型
        dataset_class_name = dataset.__class__.__name__.lower()
        if 'mvtec' in dataset_class_name:
            dataset_type = 'mvtec'
        elif 'btad' in dataset_class_name:
            dataset_type = 'btad'
        elif 'visa' in dataset_class_name:
            dataset_type = 'visa'
        else:
            dataset_type = 'generic'

    # 根据数据集类型选择合适的包装器
    if dataset_type == 'mvtec':
        return SCLMVTecWrapper(dataset, sam_suffix)
    elif dataset_type == 'btad':
        return SCLBTADWrapper(dataset, sam_suffix)
    elif dataset_type == 'visa':
        return SCLVISAWrapper(dataset, sam_suffix)
    else:
        return SCLDatasetWrapper(dataset, add_image_paths=True)


def create_scl_dataloader(dataset: Dataset,
                         batch_size: int = 8,
                         shuffle: bool = True,
                         num_workers: int = 0,
                         dataset_type: str = 'auto',
                         sam_suffix: str = '-sam-b') -> DataLoader:
    """
    创建SCL兼容的数据加载器

    Args:
        dataset: 原始数据集
        batch_size: 批次大小
        shuffle: 是否打乱数据
        num_workers: 工作进程数
        dataset_type: 数据集类型
        sam_suffix: SAM文件后缀

    Returns:
        SCL兼容的数据加载器
    """
    scl_dataset = wrap_dataset_for_scl(dataset, dataset_type, sam_suffix)

    return DataLoader(
        scl_dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        collate_fn=scl_collate_fn
    )


def scl_collate_fn(batch):
    """
    SCL数据加载器的collate函数

    Args:
        batch: 批次数据

    Returns:
        整理后的批次数据
    """
    # 分离不同类型的数据
    images = []
    masks = []
    labels = []
    is_anomaly = []
    image_paths = []
    classnames = []
    sam_paths = []

    for item in batch:
        images.append(item['image'])

        if 'mask' in item:
            masks.append(item['mask'])
        else:
            # 创建默认掩码
            masks.append(torch.zeros_like(item['image'][:1]))  # 单通道掩码

        if 'label' in item:
            labels.append(item['label'])
        else:
            labels.append(0)

        if 'is_anomaly' in item:
            is_anomaly.append(item['is_anomaly'])
        else:
            is_anomaly.append(False)

        if 'image_path' in item:
            image_paths.append(item['image_path'])
        else:
            image_paths.append('')

        if 'classname' in item:
            classnames.append(item['classname'])
        else:
            classnames.append('unknown')

        if 'sam_path' in item:
            sam_paths.append(item['sam_path'])
        else:
            sam_paths.append('')

    # 堆叠张量
    result = {
        'image': torch.stack(images),
        'mask': torch.stack(masks),
        'label': torch.tensor(labels),
        'is_anomaly': torch.tensor(is_anomaly),
        'image_path': image_paths,
        'classname': classnames,
    }

    # 只有当所有项目都有SAM路径时才添加
    if all(sam_paths):
        result['sam_path'] = sam_paths

    return result


# 便捷的数据集创建函数
def create_scl_mvtec_dataset(source: str, classname: str, split: str = 'train', **kwargs):
    """
    创建SCL兼容的MVTec数据集

    Args:
        source: MVTec数据路径
        classname: 类别名称
        split: 数据分割 ('train' 或 'test')
        **kwargs: 其他参数

    Returns:
        SCL包装的MVTec数据集
    """
    from datasets.mvtec import MVTecDataset, DatasetSplit

    dataset_split = DatasetSplit.TRAIN if split == 'train' else DatasetSplit.TEST
    mvtec_dataset = MVTecDataset(source, classname, split=dataset_split, **kwargs)

    return SCLMVTecWrapper(mvtec_dataset)


def create_scl_btad_dataset(source: str, classname: str, split: str = 'train', **kwargs):
    """
    创建SCL兼容的BTAD数据集

    Args:
        source: BTAD数据路径
        classname: 类别名称
        split: 数据分割
        **kwargs: 其他参数

    Returns:
        SCL包装的BTAD数据集
    """
    from datasets.btad import BTADDataset, DatasetSplit

    dataset_split = DatasetSplit.TRAIN if split == 'train' else DatasetSplit.TEST
    btad_dataset = BTADDataset(source, classname, split=dataset_split, **kwargs)

    return SCLBTADWrapper(btad_dataset)