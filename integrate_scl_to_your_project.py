"""
专门针对你的异常检测项目的SCL集成脚本

根据你提供的项目结构，这个脚本将帮助你快速集成SCL模块到现有项目中。

项目结构:
ANOMALYDETECTION-SOFTPATCH-MAIN/
├── BTAD/
├── images/
├── ITD/
├── mvtec_anomaly_detection/
├── noise_index/
├── result/
└── src/
    ├── _pycache_/
    ├── datasets/
    ├── __init__.py
    ├── btad.py
    ├── itd.py
    ├── mvtec.py
    ├── __init__.py
    ├── backbones.py
    ├── metrics.py
    ├── multi_variate_gaussian.py
    ├── sampler.py
    ├── sd.py
    ├── softpatch.py
    ├── utils.py
    └── main.py
"""

import os
import sys
import shutil
from pathlib import Path


def setup_scl_in_project(project_root: str):
    """
    在你的项目中设置SCL模块
    
    Args:
        project_root: 项目根目录路径
    """
    project_path = Path(project_root)
    src_path = project_path / "src"
    
    if not src_path.exists():
        print(f"错误: 找不到src目录在 {project_path}")
        return False
    
    print(f"在项目 {project_path} 中设置SCL模块...")
    
    # 1. 复制SCL文件到src目录
    scl_files = [
        "scl_module.py",
        "scl_integration.py", 
        "scl_example_usage.py"
    ]
    
    for file_name in scl_files:
        if os.path.exists(file_name):
            dest_path = src_path / file_name
            shutil.copy2(file_name, dest_path)
            print(f"✓ 复制 {file_name} 到 {dest_path}")
        else:
            print(f"⚠ 警告: 找不到文件 {file_name}")
    
    # 2. 创建SCL配置文件
    create_scl_config(src_path)
    
    # 3. 修改现有文件以支持SCL
    modify_existing_files(src_path)
    
    # 4. 创建使用示例
    create_usage_examples(src_path)
    
    print("✓ SCL模块设置完成!")
    return True


def create_scl_config(src_path: Path):
    """创建SCL配置文件"""
    config_content = '''"""
SCL Configuration for Your Project
你的项目的SCL配置
"""

# SCL模块配置
SCL_CONFIG = {
    # 基本参数
    'temperature': 0.5,          # 对比学习温度参数
    'feature_dim': 768,          # 特征维度 (根据你的模型调整)
    'patch_size': 14,            # 特征图块大小
    'use_sam_guidance': True,    # 是否使用SAM指导
    
    # 训练参数
    'lr': 0.0005,               # 学习率
    'weight_decay': 1e-4,       # 权重衰减
    'epochs': 10,               # SCL训练轮数
    'clip_grad': 1.0,           # 梯度裁剪
    
    # 数据集特定参数
    'mvtec_sam_suffix': '-sam-b',     # MVTec数据集SAM文件后缀
    'btad_sam_suffix': '-sam-b',      # BTAD数据集SAM文件后缀
    'itd_sam_suffix': '-sam-b',       # ITD数据集SAM文件后缀
}

# 数据集路径配置
DATASET_PATHS = {
    'mvtec': '../mvtec_anomaly_detection',
    'btad': '../BTAD', 
    'itd': '../ITD'
}

# SCL损失权重 (可以根据实验调整)
SCL_LOSS_WEIGHTS = {
    'mvtec': 0.1,    # MVTec数据集的SCL损失权重
    'btad': 0.1,     # BTAD数据集的SCL损失权重  
    'itd': 0.1,      # ITD数据集的SCL损失权重
}
'''
    
    config_path = src_path / "scl_config.py"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    print(f"✓ 创建配置文件 {config_path}")


def modify_existing_files(src_path: Path):
    """修改现有文件以支持SCL"""
    
    # 1. 修改main.py以支持SCL
    modify_main_py(src_path)
    
    # 2. 修改数据集文件以返回图片路径
    modify_dataset_files(src_path)
    
    # 3. 修改softpatch.py以集成SCL
    modify_softpatch_py(src_path)


def modify_main_py(src_path: Path):
    """修改main.py以支持SCL训练"""
    main_py_path = src_path / "main.py"
    
    if not main_py_path.exists():
        print(f"⚠ 警告: 找不到 {main_py_path}")
        return
    
    # 创建SCL增强版本的main.py
    scl_main_content = '''"""
SCL Enhanced Main Script
增强了SCL功能的主脚本

在原有main.py基础上添加SCL训练选项
"""

import argparse
import sys
from pathlib import Path

# 添加SCL相关导入
try:
    from scl_integration import SCLIntegratedModel, SCLPatchCoreIntegration
    from scl_config import SCL_CONFIG, SCL_LOSS_WEIGHTS
    SCL_AVAILABLE = True
except ImportError:
    print("警告: SCL模块未找到，将使用原始训练方式")
    SCL_AVAILABLE = False

# 导入你的原始模块
from softpatch import SoftPatch  # 假设你的主模型在这里
# 根据你的实际文件结构调整导入


def add_scl_arguments(parser):
    """添加SCL相关的命令行参数"""
    scl_group = parser.add_argument_group('SCL Options')
    scl_group.add_argument('--use_scl', action='store_true', 
                          help='Enable Structure-based Contrastive Learning')
    scl_group.add_argument('--scl_temperature', type=float, default=0.5,
                          help='Temperature for contrastive loss')
    scl_group.add_argument('--scl_epochs', type=int, default=10,
                          help='Number of SCL training epochs')
    scl_group.add_argument('--scl_lr', type=float, default=0.0005,
                          help='Learning rate for SCL training')
    scl_group.add_argument('--scl_weight', type=float, default=0.1,
                          help='Weight for SCL loss')
    return parser


def create_scl_model(base_model, args):
    """创建SCL增强模型"""
    if not SCL_AVAILABLE or not args.use_scl:
        return base_model
    
    scl_config = SCL_CONFIG.copy()
    scl_config.update({
        'temperature': args.scl_temperature,
        'feature_dim': getattr(args, 'feature_dim', 768),
    })
    
    scl_model = SCLIntegratedModel(base_model, scl_config)
    print(f"✓ SCL模块已启用，配置: {scl_config}")
    
    return scl_model


def train_with_scl(model, train_loader, args):
    """使用SCL进行训练"""
    if not args.use_scl:
        # 使用原始训练方法
        return train_original(model, train_loader, args)
    
    print("开始SCL训练...")
    
    # 设置SCL训练模式
    model.set_training_mode('scl')
    model.train()
    
    # 创建优化器
    import torch
    optimizer = torch.optim.Adam(model.parameters(), lr=args.scl_lr)
    
    for epoch in range(args.scl_epochs):
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, batch in enumerate(train_loader):
            # 确保batch包含image_path
            if isinstance(batch, dict):
                images = batch['image']
                image_paths = batch.get('image_path', None)
            else:
                images = batch
                image_paths = None
            
            if torch.cuda.is_available():
                images = images.cuda()
            
            # SCL前向传播
            output = model(images, image_paths)
            scl_loss = output.get('scl_loss', torch.tensor(0.0))
            
            # 反向传播
            optimizer.zero_grad()
            if scl_loss != 0:
                scl_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                optimizer.step()
            
            total_loss += scl_loss.item()
            num_batches += 1
            
            if batch_idx % 10 == 0:
                print(f"Epoch {epoch}, Batch {batch_idx}: SCL Loss = {scl_loss.item():.6f}")
        
        avg_loss = total_loss / max(num_batches, 1)
        print(f"Epoch {epoch} 完成: 平均SCL损失 = {avg_loss:.6f}")
    
    print("SCL训练完成!")


def train_original(model, train_loader, args):
    """原始训练方法 (你需要根据实际情况实现)"""
    print("使用原始训练方法...")
    # 在这里实现你的原始训练逻辑
    pass


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Anomaly Detection with Optional SCL')
    
    # 添加你的原始参数
    parser.add_argument('--dataset', type=str, choices=['mvtec', 'btad', 'itd'], 
                       default='mvtec', help='Dataset to use')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    # ... 添加其他原始参数
    
    # 添加SCL参数
    parser = add_scl_arguments(parser)
    
    args = parser.parse_args()
    
    # 创建数据加载器 (你需要根据实际情况实现)
    train_loader = create_dataloader(args)
    
    # 创建基础模型 (你需要根据实际情况实现)
    base_model = create_base_model(args)
    
    # 创建SCL增强模型
    model = create_scl_model(base_model, args)
    
    # 训练
    train_with_scl(model, train_loader, args)
    
    print("训练完成!")


def create_dataloader(args):
    """创建数据加载器 (需要根据你的实际代码实现)"""
    # 这里需要根据你的实际数据加载代码来实现
    # 确保返回的数据包含image_path字段
    pass


def create_base_model(args):
    """创建基础模型 (需要根据你的实际代码实现)"""
    # 这里需要根据你的实际模型代码来实现
    pass


if __name__ == "__main__":
    main()
'''
    
    scl_main_path = src_path / "main_with_scl.py"
    with open(scl_main_path, 'w', encoding='utf-8') as f:
        f.write(scl_main_content)
    print(f"✓ 创建SCL增强版主脚本 {scl_main_path}")


def modify_dataset_files(src_path: Path):
    """修改数据集文件以返回图片路径"""
    dataset_files = ['mvtec.py', 'btad.py', 'itd.py']
    
    for dataset_file in dataset_files:
        file_path = src_path / dataset_file
        if file_path.exists():
            create_scl_dataset_wrapper(src_path, dataset_file)


def create_scl_dataset_wrapper(src_path: Path, dataset_file: str):
    """为数据集文件创建SCL包装器"""
    wrapper_content = f'''"""
SCL Dataset Wrapper for {dataset_file}
{dataset_file}的SCL数据集包装器

这个包装器确保数据集返回图片路径，以便SCL模块使用
"""

# 导入原始数据集类
from {dataset_file.replace('.py', '')} import *

class SCLDatasetWrapper:
    """SCL数据集包装器"""
    
    def __init__(self, original_dataset):
        self.dataset = original_dataset
        
    def __len__(self):
        return len(self.dataset)
    
    def __getitem__(self, idx):
        # 获取原始数据
        item = self.dataset[idx]
        
        # 确保返回字典格式
        if isinstance(item, dict):
            result = item.copy()
        else:
            # 如果原始数据不是字典，转换为字典
            if isinstance(item, (list, tuple)) and len(item) >= 2:
                result = {{'image': item[0], 'label': item[1]}}
            else:
                result = {{'image': item, 'label': 0}}
        
        # 添加图片路径 (你需要根据实际情况调整)
        if 'image_path' not in result:
            # 尝试从数据集获取路径
            if hasattr(self.dataset, 'image_paths'):
                result['image_path'] = self.dataset.image_paths[idx]
            elif hasattr(self.dataset, 'samples'):
                result['image_path'] = self.dataset.samples[idx][0]
            else:
                # 生成默认路径
                result['image_path'] = f"dataset/{dataset_file.replace('.py', '')}/image_{{idx:06d}}.png"
        
        return result


def wrap_dataset_for_scl(dataset):
    """包装数据集以支持SCL"""
    return SCLDatasetWrapper(dataset)
'''
    
    wrapper_path = src_path / f"scl_{dataset_file}"
    with open(wrapper_path, 'w', encoding='utf-8') as f:
        f.write(wrapper_content)
    print(f"✓ 创建SCL数据集包装器 {wrapper_path}")


def modify_softpatch_py(src_path: Path):
    """修改softpatch.py以集成SCL"""
    softpatch_path = src_path / "softpatch.py"
    
    if not softpatch_path.exists():
        print(f"⚠ 警告: 找不到 {softpatch_path}")
        return
    
    # 创建SCL增强版本
    scl_softpatch_content = '''"""
SCL Enhanced SoftPatch
增强了SCL功能的SoftPatch

在原有SoftPatch基础上添加SCL支持
"""

# 导入SCL模块
try:
    from scl_integration import SCLIntegratedModel
    from scl_config import SCL_CONFIG
    SCL_AVAILABLE = True
except ImportError:
    SCL_AVAILABLE = False

# 导入原始SoftPatch (你需要根据实际情况调整)
from softpatch import SoftPatch as OriginalSoftPatch


class SCLSoftPatch(OriginalSoftPatch):
    """SCL增强的SoftPatch"""
    
    def __init__(self, *args, use_scl=False, scl_config=None, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.use_scl = use_scl and SCL_AVAILABLE
        
        if self.use_scl:
            # 使用默认配置或提供的配置
            self.scl_config = SCL_CONFIG.copy()
            if scl_config:
                self.scl_config.update(scl_config)
            
            # 包装模型以支持SCL
            self.model = SCLIntegratedModel(self.model, self.scl_config)
            print("✓ SCL已集成到SoftPatch")
    
    def extract_features(self, x):
        """提取特征用于SCL"""
        # 你需要根据SoftPatch的实际实现来调整这个方法
        # 这里是一个示例实现
        if hasattr(self.model, 'extract_features'):
            return self.model.extract_features(x)
        else:
            # 默认实现
            with torch.no_grad():
                features = self.model(x)
                if isinstance(features, dict):
                    return features.get('features', features.get('output', features))
                return features
    
    def train_with_scl(self, train_loader, epochs=10, lr=0.0005):
        """使用SCL训练"""
        if not self.use_scl:
            print("SCL未启用，使用原始训练方法")
            return self.train(train_loader, epochs)
        
        print("开始SCL训练...")
        
        # 设置SCL训练模式
        self.model.set_training_mode('scl')
        self.model.train()
        
        # 创建优化器
        import torch
        optimizer = torch.optim.Adam(self.model.parameters(), lr=lr)
        
        for epoch in range(epochs):
            total_loss = 0.0
            num_batches = 0
            
            for batch_idx, batch in enumerate(train_loader):
                # 处理批次数据
                if isinstance(batch, dict):
                    images = batch['image']
                    image_paths = batch.get('image_path', None)
                else:
                    images = batch
                    image_paths = None
                
                if torch.cuda.is_available():
                    images = images.cuda()
                
                # SCL前向传播
                output = self.model(images, image_paths)
                scl_loss = output.get('scl_loss', torch.tensor(0.0))
                
                # 反向传播
                optimizer.zero_grad()
                if scl_loss != 0:
                    scl_loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                    optimizer.step()
                
                total_loss += scl_loss.item()
                num_batches += 1
                
                if batch_idx % 10 == 0:
                    print(f"Epoch {epoch}, Batch {batch_idx}: SCL Loss = {scl_loss.item():.6f}")
            
            avg_loss = total_loss / max(num_batches, 1)
            print(f"Epoch {epoch} 完成: 平均SCL损失 = {avg_loss:.6f}")
        
        print("SCL训练完成!")
        
        # 切换回正常模式
        self.model.set_training_mode('normal')
'''
    
    scl_softpatch_path = src_path / "scl_softpatch.py"
    with open(scl_softpatch_path, 'w', encoding='utf-8') as f:
        f.write(scl_softpatch_content)
    print(f"✓ 创建SCL增强版SoftPatch {scl_softpatch_path}")


def create_usage_examples(src_path: Path):
    """创建针对你项目的使用示例"""
    example_content = '''"""
针对你的项目的SCL使用示例

这个文件展示了如何在你的具体项目中使用SCL模块
"""

import torch
from torch.utils.data import DataLoader

# 导入你的模块
from scl_softpatch import SCLSoftPatch
from scl_config import SCL_CONFIG, DATASET_PATHS
from scl_mvtec import wrap_dataset_for_scl  # 根据你使用的数据集调整


def example_mvtec_with_scl():
    """MVTec数据集使用SCL的示例"""
    
    # 1. 创建数据集
    # 这里需要根据你的实际数据集代码调整
    from mvtec import MVTecDataset  # 假设你有这个类
    
    dataset = MVTecDataset(root=DATASET_PATHS['mvtec'], train=True)
    scl_dataset = wrap_dataset_for_scl(dataset)
    dataloader = DataLoader(scl_dataset, batch_size=8, shuffle=True)
    
    # 2. 创建SCL增强的模型
    model = SCLSoftPatch(
        use_scl=True,
        scl_config=SCL_CONFIG
    )
    
    # 3. SCL训练
    model.train_with_scl(dataloader, epochs=10, lr=0.0005)
    
    print("MVTec SCL训练完成!")


def example_btad_with_scl():
    """BTAD数据集使用SCL的示例"""
    
    # 类似MVTec的实现
    from btad import BTADDataset  # 假设你有这个类
    
    dataset = BTADDataset(root=DATASET_PATHS['btad'], train=True)
    scl_dataset = wrap_dataset_for_scl(dataset)
    dataloader = DataLoader(scl_dataset, batch_size=8, shuffle=True)
    
    model = SCLSoftPatch(use_scl=True, scl_config=SCL_CONFIG)
    model.train_with_scl(dataloader, epochs=10)
    
    print("BTAD SCL训练完成!")


def example_custom_training_loop():
    """自定义训练循环示例"""
    
    # 创建模型
    model = SCLSoftPatch(use_scl=True)
    
    # 创建数据加载器 (根据你的实际情况调整)
    # dataloader = create_your_dataloader()
    
    # 自定义训练循环
    model.model.set_training_mode('scl')
    optimizer = torch.optim.Adam(model.model.parameters(), lr=0.0005)
    
    for epoch in range(10):
        for batch in dataloader:
            images = batch['image']
            image_paths = batch['image_path']
            
            # SCL前向传播
            output = model.model(images, image_paths)
            scl_loss = output['scl_loss']
            
            # 你的原始损失
            original_output = model.model.base_model(images)
            original_loss = compute_your_loss(original_output, batch['label'])
            
            # 组合损失
            total_loss = original_loss + 0.1 * scl_loss
            
            # 反向传播
            optimizer.zero_grad()
            total_loss.backward()
            optimizer.step()
            
            print(f"Epoch {epoch}: Total Loss = {total_loss.item():.6f}, "
                  f"SCL Loss = {scl_loss.item():.6f}")


if __name__ == "__main__":
    print("运行SCL使用示例...")
    
    # 选择运行哪个示例
    example_mvtec_with_scl()
    # example_btad_with_scl()
    # example_custom_training_loop()
'''
    
    example_path = src_path / "scl_usage_examples.py"
    with open(example_path, 'w', encoding='utf-8') as f:
        f.write(example_content)
    print(f"✓ 创建使用示例 {example_path}")


def main():
    """主函数"""
    print("SCL模块集成工具")
    print("=" * 50)
    
    # 获取项目路径
    project_root = input("请输入你的项目根目录路径 (或按Enter使用当前目录): ").strip()
    if not project_root:
        project_root = "."
    
    # 检查项目结构
    project_path = Path(project_root).resolve()
    if not project_path.exists():
        print(f"错误: 项目路径 {project_path} 不存在")
        return
    
    src_path = project_path / "src"
    if not src_path.exists():
        print(f"错误: 找不到src目录在 {project_path}")
        print("请确保你在正确的项目根目录中运行此脚本")
        return
    
    print(f"项目路径: {project_path}")
    print(f"源代码路径: {src_path}")
    
    # 确认继续
    confirm = input("是否继续集成SCL模块? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("取消集成")
        return
    
    # 执行集成
    success = setup_scl_in_project(str(project_path))
    
    if success:
        print("\n" + "=" * 50)
        print("✓ SCL模块集成完成!")
        print("\n下一步:")
        print("1. 检查生成的配置文件: src/scl_config.py")
        print("2. 根据你的模型调整特征提取方法")
        print("3. 准备SAM分割数据 (如果使用SAM指导)")
        print("4. 运行示例: python src/scl_usage_examples.py")
        print("5. 使用SCL训练: python src/main_with_scl.py --use_scl")
    else:
        print("❌ SCL模块集成失败")


if __name__ == "__main__":
    main()

