"""
针对你的项目的SCL使用示例

这个文件展示了如何在你的具体项目中使用SCL模块
"""

import torch
from torch.utils.data import DataLoader
import os
import sys

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)
sys.path.insert(0, current_dir)

# 导入你的模块
from scl_softpatch import create_scl_softpatch
from scl_config import SCL_CONFIG, DATASET_PATHS
from scl_dataset_wrapper import wrap_dataset_for_scl, SCLDatasetWrapper
from backbones import load


def example_mvtec_with_scl():
    """MVTec数据集使用SCL的示例"""

    try:
        # 1. 创建数据集
        from datasets.mvtec import MVTecDataset, DatasetSplit

        dataset = MVTecDataset(
            source=DATASET_PATHS['mvtec'],
            classname='bottle',  # 示例类别
            split=DatasetSplit.TRAIN
        )
        scl_dataset = wrap_dataset_for_scl(dataset, dataset_type='mvtec')
        dataloader = DataLoader(scl_dataset, batch_size=8, shuffle=True)

        # 2. 创建SCL增强的模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = create_scl_softpatch(device=device, use_scl=True, scl_config=SCL_CONFIG)

        # 加载backbone
        backbone = load("wideresnet50")
        model.load(
            backbone=backbone,
            device=device,
            input_shape=(3, 224, 224),
            layers_to_extract_from=("layer2", "layer3"),
        )

        # 3. SCL训练
        model.fit_with_scl(dataloader, scl_epochs=10, scl_lr=0.0005)

        print("MVTec SCL训练完成!")

    except ImportError as e:
        print(f"MVTec数据集导入失败: {e}")
        print("请确保MVTec数据集模块存在")
    except Exception as e:
        print(f"MVTec SCL训练失败: {e}")


def example_btad_with_scl():
    """BTAD数据集使用SCL的示例"""

    try:
        # 类似MVTec的实现
        from datasets.btad import BTADDataset, DatasetSplit

        dataset = BTADDataset(
            source=DATASET_PATHS['btad'],
            classname='01',  # 示例类别
            split=DatasetSplit.TRAIN
        )
        scl_dataset = wrap_dataset_for_scl(dataset, dataset_type='btad')
        dataloader = DataLoader(scl_dataset, batch_size=8, shuffle=True)

        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = create_scl_softpatch(device=device, use_scl=True, scl_config=SCL_CONFIG)

        # 加载backbone
        backbone = load("wideresnet50")
        model.load(
            backbone=backbone,
            device=device,
            input_shape=(3, 224, 224),
        )

        model.fit_with_scl(dataloader, scl_epochs=10)

        print("BTAD SCL训练完成!")

    except ImportError as e:
        print(f"BTAD数据集导入失败: {e}")
        print("请确保BTAD数据集模块存在")
    except Exception as e:
        print(f"BTAD SCL训练失败: {e}")


def example_custom_training_loop():
    """自定义训练循环示例"""

    try:
        # 创建设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 创建模型
        model = create_scl_softpatch(device=device, use_scl=True)

        # 加载backbone
        backbone = load("wideresnet50")
        model.load(
            backbone=backbone,
            device=device,
            input_shape=(3, 224, 224),
        )

        # 创建虚拟数据加载器用于演示
        from torch.utils.data import TensorDataset
        dummy_images = torch.randn(20, 3, 224, 224)
        dummy_labels = torch.randint(0, 2, (20,))
        dummy_dataset = TensorDataset(dummy_images, dummy_labels)
        scl_dataset = SCLDatasetWrapper(dummy_dataset)
        dataloader = DataLoader(scl_dataset, batch_size=4, shuffle=True)

        # 自定义训练循环
        if model.scl_integrated_model:
            model.scl_integrated_model.set_training_mode('scl')
            optimizer = torch.optim.Adam(model.scl_integrated_model.scl_module.parameters(), lr=0.0005)

            for epoch in range(2):  # 减少epoch用于演示
                for batch_idx, batch in enumerate(dataloader):
                    images = batch['image'].to(device)
                    image_paths = batch.get('image_path', [])

                    # SCL前向传播
                    output = model.scl_integrated_model(images, image_paths)
                    scl_loss = output.get('scl_loss', torch.tensor(0.0))

                    # 反向传播
                    optimizer.zero_grad()
                    if scl_loss.item() > 0:
                        scl_loss.backward()
                        optimizer.step()

                    if batch_idx % 2 == 0:
                        print(f"Epoch {epoch}, Batch {batch_idx}: SCL Loss = {scl_loss.item():.6f}")

        print("自定义训练循环完成!")

    except Exception as e:
        print(f"自定义训练循环失败: {e}")
        import traceback
        traceback.print_exc()


def example_simple_scl_demo():
    """简单的SCL演示示例"""

    try:
        print("运行简单SCL演示...")

        # 创建虚拟数据
        from torch.utils.data import TensorDataset
        dummy_images = torch.randn(10, 3, 224, 224)
        dummy_labels = torch.randint(0, 2, (10,))
        dummy_dataset = TensorDataset(dummy_images, dummy_labels)
        scl_dataset = SCLDatasetWrapper(dummy_dataset)
        dataloader = DataLoader(scl_dataset, batch_size=4, shuffle=True)

        # 创建SCL模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = create_scl_softpatch(device=device, use_scl=True)

        # 加载backbone
        backbone = load("wideresnet50")
        model.load(
            backbone=backbone,
            device=device,
            input_shape=(3, 224, 224),
        )

        print("✓ SCL模型创建成功")
        print("✓ 简单SCL演示完成")

    except Exception as e:
        print(f"简单SCL演示失败: {e}")


if __name__ == "__main__":
    print("运行SCL使用示例...")

    # 运行简单演示
    example_simple_scl_demo()

    # 其他示例（需要真实数据集）
    print("\n其他示例需要真实数据集:")
    print("- example_mvtec_with_scl(): 需要MVTec数据集")
    print("- example_btad_with_scl(): 需要BTAD数据集")
    print("- example_custom_training_loop(): 自定义训练循环")

    # 如果有真实数据集，可以取消注释运行
    # example_mvtec_with_scl()
    # example_btad_with_scl()
    # example_custom_training_loop()
