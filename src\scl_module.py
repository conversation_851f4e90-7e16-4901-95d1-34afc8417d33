"""
Structure-based Contrastive Learning (SCL) Module
结构化对比学习模块

这个模块实现了基于结构的对比学习，用于异常检测任务。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import os
from pathlib import Path


class SCLModule(nn.Module):
    """
    Structure-based Contrastive Learning Module
    结构化对比学习模块
    """

    def __init__(self,
                 feature_dim: int = 768,
                 temperature: float = 0.5,
                 patch_size: int = 14,
                 use_sam_guidance: bool = True):
        """
        初始化SCL模块

        Args:
            feature_dim: 特征维度
            temperature: 对比学习温度参数
            patch_size: 特征图块大小
            use_sam_guidance: 是否使用SAM指导
        """
        super(SCLModule, self).__init__()

        self.feature_dim = feature_dim
        self.temperature = temperature
        self.patch_size = patch_size
        self.use_sam_guidance = use_sam_guidance

        # 特征投影层
        self.projection_head = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, feature_dim // 4),
            nn.ReLU(),
            nn.Linear(feature_dim // 4, 128)
        )

        # 结构感知注意力机制
        self.structure_attention = nn.MultiheadAttention(
            embed_dim=128,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )

        # SAM指导权重
        if use_sam_guidance:
            self.sam_weight_net = nn.Sequential(
                nn.Conv2d(1, 16, 3, padding=1),
                nn.ReLU(),
                nn.Conv2d(16, 32, 3, padding=1),
                nn.ReLU(),
                nn.AdaptiveAvgPool2d(1),
                nn.Flatten(),
                nn.Linear(32, 1),
                nn.Sigmoid()
            )

    def forward(self,
                features: torch.Tensor,
                sam_masks: Optional[torch.Tensor] = None,
                image_paths: Optional[List[str]] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            features: 输入特征 [B, N, D]
            sam_masks: SAM分割掩码 [B, H, W]
            image_paths: 图像路径列表

        Returns:
            包含SCL损失和其他输出的字典
        """
        batch_size, num_patches, _ = features.shape

        # 特征投影
        projected_features = self.projection_head(features)  # [B, N, 128]

        # 结构感知注意力
        attended_features, attention_weights = self.structure_attention(
            projected_features, projected_features, projected_features
        )

        # 计算对比学习损失
        contrastive_loss = self._compute_contrastive_loss(attended_features)

        # 如果有SAM指导，计算SAM指导损失
        sam_loss = torch.tensor(0.0, device=features.device)
        if self.use_sam_guidance and sam_masks is not None:
            sam_loss = self._compute_sam_guided_loss(
                attended_features, sam_masks, image_paths
            )

        # 总损失
        total_loss = contrastive_loss + 0.1 * sam_loss

        return {
            'scl_loss': total_loss,
            'contrastive_loss': contrastive_loss,
            'sam_loss': sam_loss,
            'projected_features': projected_features,
            'attended_features': attended_features,
            'attention_weights': attention_weights
        }

    def _compute_contrastive_loss(self, features: torch.Tensor) -> torch.Tensor:
        """
        计算对比学习损失

        Args:
            features: 特征张量 [B, N, D]

        Returns:
            对比学习损失
        """
        batch_size, num_patches, feature_dim = features.shape

        # 归一化特征
        features_norm = F.normalize(features, dim=-1)

        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features_norm, features_norm.transpose(-2, -1))
        similarity_matrix = similarity_matrix / self.temperature

        # 创建正样本掩码（同一图像内的不同patch）
        positive_mask = torch.eye(num_patches, device=features.device).unsqueeze(0).repeat(batch_size, 1, 1)

        # 计算对比损失
        exp_sim = torch.exp(similarity_matrix)

        # 分母：所有负样本的和
        denominator = torch.sum(exp_sim * (1 - positive_mask), dim=-1, keepdim=True) + 1e-8

        # 分子：正样本
        numerator = exp_sim * positive_mask

        # 对比损失
        contrastive_loss = -torch.log(numerator / (numerator + denominator) + 1e-8)
        contrastive_loss = contrastive_loss * positive_mask
        contrastive_loss = torch.sum(contrastive_loss) / torch.sum(positive_mask)

        return contrastive_loss

    def _compute_sam_guided_loss(self,
                                features: torch.Tensor,
                                sam_masks: torch.Tensor,
                                image_paths: Optional[List[str]] = None) -> torch.Tensor:
        """
        计算SAM指导损失

        Args:
            features: 特征张量 [B, N, D]
            sam_masks: SAM掩码 [B, H, W]
            image_paths: 图像路径列表

        Returns:
            SAM指导损失
        """
        if sam_masks is None:
            return torch.tensor(0.0, device=features.device)

        batch_size = features.shape[0]

        # 将SAM掩码调整到patch大小
        sam_masks_resized = F.interpolate(
            sam_masks.unsqueeze(1).float(),
            size=(self.patch_size, self.patch_size),
            mode='bilinear',
            align_corners=False
        ).squeeze(1)  # [B, patch_size, patch_size]

        # 展平为patch级别的权重
        sam_weights = sam_masks_resized.view(batch_size, -1)  # [B, N]

        # 使用SAM权重网络处理
        sam_guidance_weights = self.sam_weight_net(sam_masks.unsqueeze(1))  # [B, 1]

        # 计算加权特征
        weighted_features = features * sam_weights.unsqueeze(-1)

        # SAM指导损失：鼓励结构一致性
        sam_loss = F.mse_loss(
            torch.mean(weighted_features, dim=1),
            torch.mean(features, dim=1)
        )

        return sam_loss * sam_guidance_weights.mean()

    def extract_structure_features(self, features: torch.Tensor) -> torch.Tensor:
        """
        提取结构特征

        Args:
            features: 输入特征 [B, N, D]

        Returns:
            结构特征 [B, N, 128]
        """
        with torch.no_grad():
            projected_features = self.projection_head(features)
            attended_features, _ = self.structure_attention(
                projected_features, projected_features, projected_features
            )
            return attended_features


class SAMGuidanceLoader:
    """
    SAM指导数据加载器
    用于加载和处理SAM分割掩码
    """

    def __init__(self, sam_suffix: str = '-sam-b'):
        """
        初始化SAM指导加载器

        Args:
            sam_suffix: SAM文件后缀
        """
        self.sam_suffix = sam_suffix

    def load_sam_mask(self, image_path: str) -> Optional[torch.Tensor]:
        """
        加载SAM掩码

        Args:
            image_path: 图像路径

        Returns:
            SAM掩码张量或None
        """
        try:
            # 构建SAM文件路径
            image_path = Path(image_path)
            sam_path = image_path.parent / f"{image_path.stem}{self.sam_suffix}.npy"

            if sam_path.exists():
                # 加载SAM掩码
                sam_mask = np.load(sam_path)
                return torch.from_numpy(sam_mask).float()
            else:
                return None

        except Exception as e:
            print(f"加载SAM掩码失败 {image_path}: {e}")
            return None

    def load_batch_sam_masks(self, image_paths: List[str]) -> Optional[torch.Tensor]:
        """
        批量加载SAM掩码

        Args:
            image_paths: 图像路径列表

        Returns:
            批量SAM掩码张量或None
        """
        if not image_paths:
            return None

        sam_masks = []
        for image_path in image_paths:
            sam_mask = self.load_sam_mask(image_path)
            if sam_mask is not None:
                sam_masks.append(sam_mask)
            else:
                # 如果没有SAM掩码，创建全1掩码
                sam_masks.append(torch.ones(224, 224))  # 默认大小

        if sam_masks:
            return torch.stack(sam_masks)
        else:
            return None


def create_scl_module(config: Dict) -> SCLModule:
    """
    创建SCL模块的工厂函数

    Args:
        config: 配置字典

    Returns:
        SCL模块实例
    """
    return SCLModule(
        feature_dim=config.get('feature_dim', 768),
        temperature=config.get('temperature', 0.5),
        patch_size=config.get('patch_size', 14),
        use_sam_guidance=config.get('use_sam_guidance', True)
    )