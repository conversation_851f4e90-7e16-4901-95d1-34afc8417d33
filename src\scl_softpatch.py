"""
SCL Enhanced SoftPatch
增强了SCL功能的SoftPatch

在原有SoftPatch基础上添加SCL支持
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Optional, Union, Any
import tqdm

# 导入SCL模块
try:
    from src.scl_integration import SCLIntegratedModel, SCLPatchCoreIntegration
    from src.scl_config import SCL_CONFIG
    SCL_AVAILABLE = True
except ImportError:
    SCL_AVAILABLE = False
    print("警告: SCL模块未找到，将使用原始SoftPatch")

# 导入原始SoftPatch
from src.softpatch import SoftPatch as OriginalSoftPatch


class SCLSoftPatch(OriginalSoftPatch):
    """SCL增强的SoftPatch"""

    def __init__(self, device, use_scl=False, scl_config=None):
        """
        初始化SCL增强的SoftPatch

        Args:
            device: 设备
            use_scl: 是否使用SCL
            scl_config: SCL配置
        """
        super().__init__(device)

        self.use_scl = use_scl and SCL_AVAILABLE
        self.scl_integrated_model = None

        if self.use_scl:
            # 使用默认配置或提供的配置
            self.scl_config = SCL_CONFIG.copy()
            if scl_config:
                self.scl_config.update(scl_config)
            print("✓ SCL配置已加载")
        else:
            self.scl_config = None

    def load(self, *args, **kwargs):
        """
        加载模型，如果启用SCL则创建SCL集成模型
        """
        # 调用原始的load方法
        super().load(*args, **kwargs)

        # 如果启用SCL，创建SCL模块（不是集成模型，避免循环引用）
        if self.use_scl:
            from src.scl_module import create_scl_module
            self.scl_module = create_scl_module(self.scl_config)
            self.scl_module.to(self.device)
            print("✓ SCL已集成到SoftPatch")
    
    def extract_features(self, x):
        """提取特征用于SCL"""
        # 使用原始SoftPatch的_embed方法
        return self._embed(x, detach=False)

    def fit_with_scl(self, training_data, scl_epochs=10, scl_lr=0.0005):
        """
        使用SCL增强的训练方法

        Args:
            training_data: 训练数据
            scl_epochs: SCL训练轮数
            scl_lr: SCL学习率
        """
        if not self.use_scl or not hasattr(self, 'scl_module'):
            print("SCL未启用，使用原始训练方法")
            return self.fit(training_data)

        print("开始SCL增强训练...")

        # 第一阶段：SCL预训练
        self._scl_pretrain(training_data, scl_epochs, scl_lr)

        # 第二阶段：原始SoftPatch训练
        print("开始原始SoftPatch训练...")
        self.fit(training_data)

        print("SCL增强训练完成!")

    def _scl_pretrain(self, training_data, epochs=10, lr=0.0005):
        """
        SCL预训练阶段

        Args:
            training_data: 训练数据
            epochs: 训练轮数
            lr: 学习率
        """
        print("开始SCL预训练...")

        # 设置SCL训练模式
        self.scl_module.train()

        # 创建优化器（只优化SCL模块）
        optimizer = torch.optim.Adam(
            self.scl_module.parameters(),
            lr=lr,
            weight_decay=self.scl_config.get('weight_decay', 1e-4)
        )

        for epoch in range(epochs):
            total_loss = 0.0
            num_batches = 0

            with tqdm.tqdm(training_data, desc=f"SCL Epoch {epoch}", leave=True) as data_iterator:
                for batch_idx, batch in enumerate(data_iterator):
                    # 处理批次数据
                    if isinstance(batch, dict):
                        images = batch['image']
                        image_paths = batch.get('image_path', None)
                    else:
                        images = batch
                        image_paths = None

                    images = images.to(torch.float).to(self.device)

                    # 提取特征用于SCL
                    features = self._embed(images, detach=False)
                    if isinstance(features, tuple):
                        features = features[0]

                    # 确保特征是正确的形状 [B, N, D]
                    if features.dim() == 2:
                        # [B*N, D] -> [B, N, D]
                        batch_size = images.shape[0]
                        num_patches = features.shape[0] // batch_size
                        features = features.view(batch_size, num_patches, -1)
                    elif features.dim() == 4:
                        # [B, D, H, W] -> [B, H*W, D]
                        B, D, H, W = features.shape
                        features = features.permute(0, 2, 3, 1).contiguous().view(B, H*W, D)

                    # SCL前向传播
                    output = self.scl_module(features, None, image_paths)
                    scl_loss = output.get('scl_loss', torch.tensor(0.0))

                    # 反向传播
                    optimizer.zero_grad()
                    if scl_loss.item() > 0:
                        scl_loss.backward()
                        torch.nn.utils.clip_grad_norm_(
                            self.scl_module.parameters(),
                            self.scl_config.get('clip_grad', 1.0)
                        )
                        optimizer.step()

                    total_loss += scl_loss.item()
                    num_batches += 1

                    # 更新进度条
                    data_iterator.set_postfix({
                        'SCL_Loss': f'{scl_loss.item():.6f}'
                    })

            avg_loss = total_loss / max(num_batches, 1)
            print(f"SCL Epoch {epoch} 完成: 平均损失 = {avg_loss:.6f}")

        print("SCL预训练完成!")

        # 切换回正常模式
        self.scl_module.eval()

    def _embed(self, images, detach=True, provide_patch_shapes=False):
        """
        重写_embed方法以支持SCL
        """
        # 使用原始的_embed方法
        return super()._embed(images, detach, provide_patch_shapes)

    def predict(self, data):
        """
        重写predict方法
        """
        # 预测时始终使用原始SoftPatch方法
        return super().predict(data)

    def get_scl_features(self, images):
        """
        获取SCL增强的特征

        Args:
            images: 输入图像

        Returns:
            SCL增强的特征
        """
        if not self.use_scl or not hasattr(self, 'scl_module'):
            print("SCL未启用")
            return None

        with torch.no_grad():
            images = images.to(torch.float).to(self.device)
            features = self._embed(images, detach=False)
            if isinstance(features, tuple):
                features = features[0]

            # 确保特征是正确的形状 [B, N, D]
            if features.dim() == 2:
                batch_size = images.shape[0]
                num_patches = features.shape[0] // batch_size
                features = features.view(batch_size, num_patches, -1)
            elif features.dim() == 4:
                B, D, H, W = features.shape
                features = features.permute(0, 2, 3, 1).contiguous().view(B, H*W, D)

            scl_features = self.scl_module.extract_structure_features(features)
            return scl_features.cpu().numpy()

    def save_scl_model(self, save_path: str):
        """
        保存SCL模型

        Args:
            save_path: 保存路径
        """
        if not self.use_scl or not hasattr(self, 'scl_module'):
            print("SCL未启用，无法保存SCL模型")
            return

        torch.save({
            'scl_module_state_dict': self.scl_module.state_dict(),
            'scl_config': self.scl_config
        }, save_path)
        print(f"SCL模型已保存到: {save_path}")

    def load_scl_model(self, load_path: str):
        """
        加载SCL模型

        Args:
            load_path: 加载路径
        """
        if not self.use_scl or not hasattr(self, 'scl_module'):
            print("SCL未启用，无法加载SCL模型")
            return

        checkpoint = torch.load(load_path, map_location=self.device)
        self.scl_module.load_state_dict(checkpoint['scl_module_state_dict'])
        print(f"SCL模型已从 {load_path} 加载")


def create_scl_softpatch(device, use_scl=True, scl_config=None):
    """
    创建SCL增强的SoftPatch的便捷函数

    Args:
        device: 设备
        use_scl: 是否使用SCL
        scl_config: SCL配置

    Returns:
        SCL增强的SoftPatch实例
    """
    return SCLSoftPatch(device=device, use_scl=use_scl, scl_config=scl_config)
